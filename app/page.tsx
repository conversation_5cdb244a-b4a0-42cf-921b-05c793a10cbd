"use client";

import React, { useCallback, useEffect, useRef, useState } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent } from '@/components/ui/card';
import {
  Dropzone,
  DropzoneContent,
  DropzoneEmptyState
} from '@/components/ui/shadcn-io/dropzone';
import { ImageIcon, X } from 'lucide-react';

interface Message {
    id: number;
    text: string;
    sender: "user" | "bot";
    image?: string; // base64 encoded image
    imageFile?: File; // for display purposes
}

export default function Home() {
    const [messages, setMessages] = useState<Message[]>([
        {id: 1, text: "Hello! How can I help you today?", sender: "bot"},
    ]);
    const [inputText, setInputText] = useState("");
    const [selectedImage, setSelectedImage] = useState<File | null>(null);
    const [isDragOver, setIsDragOver] = useState(false);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [dragCounter, setDragCounter] = useState(0);
    const [isStreaming, setIsStreaming] = useState(false);
    const scrollAreaRef = useRef<HTMLDivElement>(null);
    const handleSend = async () => {
        if (!inputText.trim() && !selectedImage) return;

        // Convert image to base64 if present
        let imageBase64 = "";
        if (selectedImage) {
            const reader = new FileReader();
            imageBase64 = await new Promise<string>((resolve) => {
                reader.onload = () => {
                    const result = reader.result as string;
                    // Remove data URL prefix to get just the base64 string
                    resolve(result.split(',')[1]);
                };
                reader.readAsDataURL(selectedImage);
            });
        }

        const userMessage: Message = {
            id: Date.now(),
            text: inputText || (selectedImage ? "Shared an image" : ""),
            sender: "user",
            image: imageBase64 || undefined,
            imageFile: selectedImage || undefined,
        };

        setMessages((prev) => [...prev, userMessage]);

        const messageText = inputText || "What do you see in this image?";
        setInputText("");
        setSelectedImage(null);

        // Add placeholder bot message for streaming
        const botMessageId = Date.now() + 1;
        setMessages((prev) => [
            ...prev,
            {
                id: botMessageId,
                text: "",
                sender: "bot",
            },
        ]);

        setIsStreaming(true);

        try {
            const response = await fetch('/api/chat/stream', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: messageText,
                    image: imageBase64 || undefined,
                    conversationHistory: messages // Send the entire conversation history
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to get response');
            }

            const reader = response.body?.getReader();
            const decoder = new TextDecoder();
            let accumulatedText = "";

            if (reader) {
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                if (data.content) {
                                    accumulatedText += data.content;
                                    setMessages((prev) =>
                                        prev.map(msg =>
                                            msg.id === botMessageId
                                                ? { ...msg, text: accumulatedText }
                                                : msg
                                        )
                                    );
                                } else if (data.done) {
                                    break;
                                } else if (data.error) {
                                    throw new Error(data.error);
                                }
                            } catch {
                                // Ignore parsing errors for incomplete chunks
                            }
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error sending message:', error);
            setMessages((prev) =>
                prev.map(msg =>
                    msg.id === botMessageId
                        ? { ...msg, text: "Sorry, I encountered an error. Please try again." }
                        : msg
                )
            );
        } finally {
            setIsStreaming(false);
        }
    };

    const handleImageUpload = (files: File[]) => {
        if (files.length > 0) {
            setSelectedImage(files[0]);
        }
    };

    const removeSelectedImage = () => {
        setSelectedImage(null);
    };

    // Drag and drop handlers with counter to prevent flickering
    const handleDragEnter = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();

        // Only process if dragging files
        if (e.dataTransfer.types.includes('Files')) {
            setDragCounter(prev => prev + 1);
            setIsDragOver(true);
        }
    }, []);

    const handleDragLeave = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();

        setDragCounter(prev => {
            const newCounter = prev - 1;
            if (newCounter === 0) {
                setIsDragOver(false);
            }
            return newCounter;
        });
    }, []);

    const handleDragOver = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
    }, []);

    const handleDrop = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();

        setDragCounter(0);
        setIsDragOver(false);

        const files = Array.from(e.dataTransfer.files);
        const imageFiles = files.filter(file => file.type.startsWith('image/'));

        if (imageFiles.length > 0) {
            setSelectedImage(imageFiles[0]); // Take the first image
        }
    }, []);

    // Auto-scroll to bottom when messages change
    useEffect(() => {
        if (scrollAreaRef.current) {
            const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
            if (scrollContainer) {
                scrollContainer.scrollTop = scrollContainer.scrollHeight;
            }
        }
    }, [messages]);

    // Reset drag state when dragging outside the window
    useEffect(() => {
        const handleWindowDragLeave = (e: DragEvent) => {
            if (e.clientX === 0 && e.clientY === 0) {
                setDragCounter(0);
                setIsDragOver(false);
            }
        };

        window.addEventListener('dragleave', handleWindowDragLeave);
        return () => window.removeEventListener('dragleave', handleWindowDragLeave);
    }, []);

    return (
        <div
            className="flex flex-col h-screen"
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
        >
            <header className="border-b px-6 py-4">
                <h1 className="text-2xl font-bold tracking-tight">Chat Interface</h1>
            </header>

            <ScrollArea ref={scrollAreaRef} className="flex-1 p-4">
                <div className="flex flex-col gap-4 max-w-3xl mx-auto">
                    {messages.map((message) => (
                        <div
                            key={message.id}
                            className={`flex ${
                                message.sender === "user" ? "justify-end" : "justify-start"
                            }`}
                        >
                            <Card
                                className={`max-w-[70%] ${
                                    message.sender === "user" ? "bg-primary" : "bg-muted"
                                }`}
                            >
                                <CardContent
                                    className={`p-3 ${
                                        message.sender === "user"
                                            ? "text-primary-foreground"
                                            : "text-muted-foreground"
                                    }`}
                                >
                                    {message.image && (
                                        <div className="mb-2">
                                            <Image
                                                src={message.imageFile ? URL.createObjectURL(message.imageFile) : `data:image/jpeg;base64,${message.image}`}
                                                alt="Uploaded image"
                                                className="max-w-full h-auto rounded-md max-h-64 object-contain"
                                                width={256}
                                                height={256}
                                                style={{ maxHeight: '16rem', objectFit: 'contain' }}
                                            />
                                        </div>
                                    )}
                                    {message.text}
                                </CardContent>
                            </Card>
                        </div>
                    ))}
                </div>
            </ScrollArea>

            <footer className="border-t p-4">
                <div className="max-w-3xl mx-auto">
                    {isDragOver && (
                        <div className="mb-4 p-6 text-center text-blue-600 bg-blue-50 border-2 border-dashed border-blue-300 rounded-lg">
                            <ImageIcon className="h-8 w-8 mx-auto mb-2" />
                            <p className="text-sm font-medium">Drop your image here to upload</p>
                        </div>
                    )}
                    {selectedImage && (
                        <div className="mb-4 p-3 border rounded-lg bg-muted/50">
                            <div className="flex items-center justify-between mb-2">
                                <span className="text-sm font-medium">Selected Image:</span>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={removeSelectedImage}
                                    className="h-6 w-6 p-0"
                                >
                                    <X className="h-4 w-4" />
                                </Button>
                            </div>
                            <Image
                                src={URL.createObjectURL(selectedImage)}
                                alt="Selected image"
                                className="max-w-full h-auto rounded-md max-h-32 object-contain"
                                width={128}
                                height={128}
                                style={{ maxHeight: '8rem', objectFit: 'contain' }}
                            />
                        </div>
                    )}
                    <div className="flex gap-2">
                        <Input
                            value={inputText}
                            onChange={(e) => setInputText(e.target.value)}
                            onKeyPress={(e) => e.key === "Enter" && !isStreaming && handleSend()}
                            placeholder={selectedImage ? "Add a message (optional)..." : "Type a message..."}
                            className="flex-1"
                            disabled={isStreaming}
                        />
                        <Dropzone
                            onDrop={handleImageUpload}
                            accept={{ 'image/*': [] }}
                            maxFiles={1}
                            className="h-9 w-9 p-0"
                        >
                            <DropzoneEmptyState className="p-0">
                                <ImageIcon className="h-4 w-4" />
                            </DropzoneEmptyState>
                            <DropzoneContent className="p-0">
                                <ImageIcon className="h-4 w-4" />
                            </DropzoneContent>
                        </Dropzone>
                        <Button onClick={handleSend} disabled={isStreaming}>
                            {isStreaming ? "Sending..." : "Send"}
                        </Button>
                    </div>
                </div>
            </footer>
        </div>
    );
}
